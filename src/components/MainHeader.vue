<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useAuthStore } from 'src/stores/auth';
import utilsConfigs from 'src/configs/utilsConfigs';
import type PsnUser from 'src/types/ums/user';
import { useGlobalStore } from 'src/stores/global';

const authStore = useAuthStore();
const route = useRoute();
const user = ref<PsnUser>();
const globalStore = useGlobalStore();
const router = useRouter();

const headerTitle = computed(() => {
  // ถ้าอยู่ในหน้าแบบทดสอบที่มี ID
  if (route.name === 'quiz-id' && route.params.id) {
    const quizId = route.params.id as string;
    const quizTitle = globalStore.getCurrentQuizTitle(quizId);
    return quizTitle || `แบบทดสอบ #${quizId}`;
  }

  // ถ้าอยู่ในส่วนของแบบทดสอบ
  if (route.path.includes('/quiz')) {
    return 'ระบบแบบทดสอบ';
  }

  // ถ้าอยู่ในส่วนของแบบสอบถาม
  if (route.path.includes('/evaluate')) {
    return 'ระบบแบบสอบถาม';
  }

  // ถ้าอยู่ในส่วนของ UMS
  if (route.path.includes('/ums')) {
    return 'จัดการสิทธิ์ในระบบ';
  }

  // หน้าหลักหรือหน้าอื่นๆ
  return 'ระบบจัดการ';
});

const isEditMode = computed(() => {
  return route.query.mode === 'edit';
});
const goToHome = async () => {
  await router.push('/');
};

onMounted(() => {
  user.value = authStore.getCurrentUser();
});
</script>

<template>
  <q-header>
    <div class="header-bg">
      <div class="left-bg"></div>
      <div class="right-bg"></div>
    </div>

    <q-toolbar class="text-black justify-between">
      <q-toolbar-title class="flex items-center q-gutter-x-sm">
        <q-btn
          padding="xs"
          style="background: transparent"
          icon="menu"
          color="white"
          flat
          @click="globalStore.toggleLeftDrawer()"
        ></q-btn>
        <q-img src="/brands/brand-white.webp" alt="brand" width="40px" height="40px"></q-img>

        <div class="text-white text-h6 q-ml-md cursor-pointer header-title-hover"
          @click="goToHome">
          {{ headerTitle }}
        </div>

        <div v-if="isEditMode" class="saving-container">
          <div class="saving-box">
            <span class="saving-text">กำลังบันทึก</span>
            <span class="animated-ellipsis">
              <span class="ellipsis-dot">.</span>
              <span class="ellipsis-dot">.</span>
              <span class="ellipsis-dot">.</span>
            </span>
          </div>
        </div>
      </q-toolbar-title>

      <div class="cursor-pointer row">
        <div class="text-white q-mr-sm q-my-auto text-caption">
          <div>Tralalero Tralala</div>
          <div>Super Admin</div>
        </div>
        <q-avatar size="40px" class="q-mx-xs">
          <img v-if="user" src="/mockup/avatar.webp" />
          <q-icon v-else name="account_circle" size="40px" />
        </q-avatar>

        <q-menu anchor="bottom right" self="top right">
          <q-card style="min-width: 200px">
            <q-list bordered separator>
              <q-item>
                <q-item-section avatar>
                  <q-icon name="account_circle" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ user?.psnFullname }}</q-item-label>
                  <q-item-label caption v-for="(sys, index) in utilsConfigs.systems" :key="index">
                    • {{ sys.sysNameTh }}
                  </q-item-label>
                </q-item-section>
              </q-item>

              <q-item clickable v-ripple @click="authStore.logout()">
                <q-item-section avatar>
                  <q-icon name="logout" color="red" />
                </q-item-section>
                <q-item-section>
                  <q-item-label class="text-red">ออกจากระบบ </q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-card>
        </q-menu>
      </div>
    </q-toolbar>
  </q-header>
</template>
<style scoped>
.header-title-hover {
  transition: opacity 0.2s ease;
  user-select: none;
}

.header-title-hover:hover {
  opacity: 0.8;
  cursor: pointer;
}

.saving-container {
  position: relative;
  margin-left: 12px;
}

.saving-box {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 6px 12px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  /* You might want to set a min-height if the content is very short */
  /* min-height: 28px; /* Example */
}

.saving-text {
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
  opacity: 0.9;
  line-height: 1;
}

.animated-ellipsis {
  display: inline-flex;
  align-items: center;
  line-height: 1;
}

.animated-ellipsis .ellipsis-dot {
  color: white;
  font-size: 0.75em;
  font-weight: bold;
  opacity: 0.8;
  animation-name: dot-wave-animation;
  animation-duration: 1.4s;
  animation-iteration-count: infinite;
  animation-timing-function: ease-in-out;
  /* line-height: 1; /* Can also be here for stability */
}

.animated-ellipsis .ellipsis-dot:nth-child(1) {
  animation-delay: 0s;
}
.animated-ellipsis .ellipsis-dot:nth-child(2) {
  animation-delay: 0.2s;
}
.animated-ellipsis .ellipsis-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes dot-wave-animation {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.7;
  }
  30% {
    transform: translateY(-4px);
    opacity: 1;
  }
}
</style>

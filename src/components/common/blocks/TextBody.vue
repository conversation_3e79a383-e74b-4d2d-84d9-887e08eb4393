<template>
  <div class="text-body-container">
    <!-- Question text input with auto-save -->
    <q-input
      v-model="questionText"
      placeholder="Enter question text..."
      dense
      outlined
      @input="onQuestionTextChange"
      class="question-text-input q-mb-md"
    />

    <!-- Answer preview (disabled) -->
    <q-input
      v-model="store.textInput"
      placeholder="คำตอบ..."
      dense
      disable
      class="answer-preview"
    />
  </div>
</template>

<script setup lang="ts">
import { inject, ref, watch } from 'vue';
import type { ItemBlockStore } from 'src/stores/item_block_store';
import type { ItemBlock } from 'src/types/models';

// Get the store instance provided by the parent ItemBlock
const store = inject<ItemBlockStore>('blockStore');
if (!store) {
  throw new Error('TextBody must be used within an ItemBlock component');
}

// Get the ItemBlock data from parent
const itemBlock = inject<ItemBlock>('itemBlock');

// Get auto-save functions from parent ItemBlock
const autoSave = inject<{
  debouncedAutoSaveQuestion: (
    questionId: number,
    field: string,
    value: string | number | boolean,
  ) => void;
  debouncedAutoSaveBlock: (field: string, value: string | number | boolean) => void;
}>('autoSave');

// Local reactive state for question text
const questionText = ref<string>('');

// Initialize question text from store textInput (for TEXTFIELD type)
if (store && store.textInput) {
  questionText.value = store.textInput;
}

// Handle question text changes with auto-save
function onQuestionTextChange(value: string) {
  // Update the store's textInput
  if (store) {
    store.textInput = value;
  }

  // Trigger auto-save if we have the necessary data
  if (autoSave && itemBlock && itemBlock.questions && itemBlock.questions.length > 0) {
    const questionId = itemBlock.questions[0]?.id;
    if (questionId) {
      // Trigger debounced auto-save for question text
      autoSave.debouncedAutoSaveQuestion(questionId, 'questionText', value);
    }
  }
}

// Watch for changes in store textInput to update local state
watch(
  () => store?.textInput,
  (newText) => {
    if (newText !== undefined && newText !== questionText.value) {
      questionText.value = newText;
    }
  },
);

// Watch for changes in itemBlock questions to update local state
watch(
  () => itemBlock?.questions?.[0]?.questionText,
  (newText) => {
    if (newText !== undefined && newText !== questionText.value) {
      questionText.value = newText;
      // Also update store textInput to keep them in sync
      if (store) {
        store.textInput = newText;
      }
    }
  },
);
</script>

<style scoped>
.text-body-container {
  width: 100%;
}

.question-text-input {
  width: 100%;
  max-width: 500px;
}

.answer-preview {
  align-items: flex-start;
  width: 360px;
}
</style>

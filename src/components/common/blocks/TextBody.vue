<template>
  <div class="text-body-container">
    <!-- Question text input with auto-save -->
    <q-input
      v-model="questionText"
      placeholder="Enter question text..."
      dense
      outlined
      @input="onQuestionTextChange"
      class="question-text-input q-mb-md"
    />

    <!-- Answer preview (disabled) -->
    <q-input
      v-model="store.textInput"
      placeholder="คำตอบ..."
      dense
      disable
      class="answer-preview"
    />
  </div>
</template>

<script setup lang="ts">
import { inject, ref, watch } from 'vue';
import type { ItemBlockStore } from 'src/stores/item_block_store';

// Get the store instance provided by the parent ItemBlock
const store = inject<ItemBlockStore>('blockStore');
if (!store) {
  throw new Error('TextBody must be used within an ItemBlock component');
}

// Get auto-save functions from parent ItemBlock
const autoSave = inject<{
  debouncedAutoSaveQuestion: (
    questionId: number,
    field: string,
    value: string | number | boolean,
  ) => void;
  debouncedAutoSaveBlock: (field: string, value: string | number | boolean) => void;
}>('autoSave');

// Local reactive state for question text
const questionText = ref<string>('');

// Initialize question text from store
if (store.questions && store.questions.length > 0) {
  questionText.value = store.questions[0]?.questionText || '';
}

// Handle question text changes with auto-save
function onQuestionTextChange(value: string) {
  if (autoSave && store.questions && store.questions.length > 0) {
    const questionId = store.questions[0]?.id;
    if (questionId) {
      // Trigger debounced auto-save for question text
      autoSave.debouncedAutoSaveQuestion(questionId, 'questionText', value);
    }
  }
}

// Watch for changes in store questions to update local state
watch(
  () => store.questions?.[0]?.questionText,
  (newText) => {
    if (newText !== undefined && newText !== questionText.value) {
      questionText.value = newText;
    }
  },
);
</script>

<style scoped>
.text-body-container {
  width: 100%;
}

.question-text-input {
  width: 100%;
  max-width: 500px;
}

.answer-preview {
  align-items: flex-start;
  width: 360px;
}
</style>

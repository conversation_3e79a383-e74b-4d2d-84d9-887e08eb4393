<template>
  <q-card @click="$emit('focus-fab')" class="q-mb-md header-block-container">
    <!-- Header with three-dot menu -->
    <div class="header-top-bar">
      <q-btn flat round color="grey-6" size="sm" class="three-dot-menu" @click.stop="toggleMenu">
        <ThreeDots size="xs" color="grey-6" />
        <q-menu v-model="showMenu" anchor="bottom middle" self="top middle">
          <q-list style="min-width: 150px">
            <q-item clickable v-close-popup @click="onDuplicate">
              <q-item-section avatar>
                <q-icon name="content_copy" />
              </q-item-section>
              <q-item-section>Duplicate</q-item-section>
            </q-item>
            <q-item clickable v-close-popup @click="onDelete">
              <q-item-section avatar>
                <q-icon name="delete" />
              </q-item-section>
              <q-item-section>Delete</q-item-section>
            </q-item>
          </q-list>
        </q-menu>
      </q-btn>
    </div>

    <!-- Title Editor -->
    <div class="input-wrapper">
      <EditorTool
        ref="titleEditor"
        :label="'หัวข้อ...'"
        :isHeader="true"
        :initialValue="titleContent"
        @update:content="updateTitle"
        @focus="isTitleFocused = true"
        @blur="handleTitleBlur"
      />
    </div>

    <!-- Description Editor -->
    <div class="input-wrapper">
      <EditorTool
        ref="descriptionEditor"
        :label="'รายละเอียด...'"
        :initialValue="descriptionContent"
        @update:content="updateDescription"
        @focus="isDescriptionFocused = true"
        @blur="handleDescriptionBlur"
      />
      <ItemBlockFooter v-if="props.index > 0" label="ข้อความ" />
    </div>
  </q-card>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { Ref } from 'vue';
import EditorTool from 'src/components/common/EditorTool.vue';
import ThreeDots from 'src/components/common/ThreeDots.vue';
import config from 'src/configs/block_creator.config';
import type { ItemBlock } from 'src/types/models';
import ItemBlockFooter from './ItemBlockFooter.vue';

const props = defineProps<{
  itemBlock: ItemBlock;
  index: number;
}>();

// Define emits
const emit = defineEmits([
  'blur',
  'focus-fab',
  'update:title',
  'update:description',
  'duplicate',
  'delete',
]);

// Refs for editors
const titleEditor = ref(null);
const descriptionEditor = ref(null);

// Content refs to track values locally
const titleContent = ref<string>(props.itemBlock.headerBody?.title || '');
const descriptionContent = ref<string>(props.itemBlock.headerBody?.description || '');

// Focus state for styling
const isTitleFocused = ref(false);
const isDescriptionFocused = ref(false);

// Menu state
const showMenu = ref(false);

// Debounce timeout refs
const titleDebounceTimeout = ref<number | null>(null);
const descriptionDebounceTimeout = ref<number | null>(null);
const DEBOUNCE_DELAY = config.SAVE_DEBOUNCE;
// Update functions for local content
function updateTitle(content: string) {
  titleContent.value = content;
  emit('update:title', content);
}

function updateDescription(content: string) {
  descriptionContent.value = content;
  emit('update:description', content);
}

// Clear existing timeout if any
function clearDebounceTimeout(timeoutRef: Ref<number | null>) {
  if (timeoutRef.value) {
    clearTimeout(timeoutRef.value);
    timeoutRef.value = null;
  }
}

// Handlers for blur events that emit to parent
function handleTitleBlur() {
  isTitleFocused.value = false;

  // Clear any existing timeout
  clearDebounceTimeout(titleDebounceTimeout);

  // Set new timeout
  titleDebounceTimeout.value = window.setTimeout(() => {
    const content = titleContent.value;

    // Only emit if content is not empty
    if (content && content.trim() !== '') {
      emit('blur', 'title', content);
    }
  }, DEBOUNCE_DELAY);
}

function handleDescriptionBlur() {
  isDescriptionFocused.value = false;

  // Clear any existing timeout
  clearDebounceTimeout(descriptionDebounceTimeout);

  // Set new timeout
  descriptionDebounceTimeout.value = window.setTimeout(() => {
    const content = descriptionContent.value;

    // Only emit if content is not empty
    if (content && content.trim() !== '') {
      emit('blur', 'description', content);
    }
  }, DEBOUNCE_DELAY);
}

// Menu handler functions
function toggleMenu() {
  showMenu.value = !showMenu.value;
}

function onDuplicate() {
  emit('duplicate');
}

function onDelete() {
  emit('delete');
}
</script>

<style scoped>
.input-wrapper {
  position: relative;
  margin: 16px;
}

.header-block-container {
  position: relative;
}

.header-top-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 8px;
}

.three-dot-menu {
  background-color: transparent;
  border: none;
  border-radius: 0;
  padding: 4px;
  cursor: grab;
  outline: none;
}

.three-dot-menu:hover {
  background-color: transparent;
}

.three-dot-menu:active {
  cursor: grabbing;
}

.three-dot-menu:focus {
  outline: none;
  box-shadow: none;
}
</style>

<template>
  <q-page>
    <draggable
      v-model="draggableBlocks"
      :component-data="{
        tag: 'div',
        type: 'transition-group',
        name: !isDragging ? 'flip-list' : null,
      }"
      item-key="id"
      handle=".three-dot-menu"
      :animation="200"
      ghost-class="ghost"
      chosen-class="chosen"
      drag-class="drag"
      @start="onDragStart"
      @end="onDragEnd"
      @change="onDragChange"
    >
      <template #item="{ element: block, index }">
        <div
          :key="block.id"
          :ref="(el) => (blockRefs[block.id] = el)"
          class="row justify-center draggable-item"
          :class="{ 'is-dragging': isDragging }"
        >
          <div class="col-auto">
            <div
              v-if="store.isSectionBlock(index) && store.totalSections > 1"
              class="col-12 section-container"
            >
              <div class="section-tab">
                ส่วนที่ {{ store.getSectionNumber(index) }} จาก {{ store.totalSections }}
              </div>
            </div>
            <div class="block-container">
              <div class="block-content full-width" @click="setSelectedBlock(block)">
                <template v-if="block.type === 'HEADER'">
                  <HeaderBlock
                    :itemBlock="block"
                    :index="index"
                    class="evaluate-item"
                    :class="{
                      'no-top-left-radius': store.isSectionBlock(index) && store.totalSections > 1,
                    }"
                    @focus-fab="store.selectedBlockId = `block-${block.id}`"
                    @duplicate="() => handleDuplicateHeaderBlock(block, index)"
                    @delete="() => onClickDeleteBlock(block, index)"
                  />
                </template>

                <template v-else-if="block.type === 'IMAGE'">
                  <ImageBlock
                    :item-block="block"
                    class="evaluate-item"
                    @focus-fab="allowFocusFab && (store.selectedBlockId = `block-${block.id}`)"
                    @duplicate="() => handleDuplicateBlock(block, index)"
                    @delete="() => onClickDeleteBlock(block, index)"
                  />
                </template>

                <template v-else>
                  <ItemBlockProvider :block-id="block.id" :item-block="block">
                    <ItemBlockComponent
                      :item-block="block"
                      :type="props.type"
                      class="evaluate-item"
                      @focus-fab="allowFocusFab && (store.selectedBlockId = `block-${block.id}`)"
                      @duplicate="() => handleDuplicateBlock(block, index)"
                      @delete="() => onClickDeleteBlock(block, index)"
                    />
                  </ItemBlockProvider>
                </template>
              </div>
            </div>
          </div>

          <div class="col-auto fixed-fab-col">
            <FloatActionBtnForBlock
              v-show="store.selectedBlockId === `block-${block.id}` && !isDragging"
              @add="() => handleAddBlockAfter(index)"
              @add-text="() => handleAddHeaderAfter(index)"
              @add-section="() => handleAddSection()"
              @add-image="(imageData) => handleAddImage(index, imageData)"
            />
          </div>
        </div>
      </template>
    </draggable>
  </q-page>
</template>

<script setup lang="ts">
// Define component name for keep-alive
defineOptions({
  name: 'block-creator',
});

import { ref, watch, nextTick, onMounted, computed, type ComponentPublicInstance } from 'vue';
import { useBlockCreatorStore } from 'src/stores/block_creator';
import type { ItemBlock } from 'src/types/models';
import HeaderBlock from './HeaderBlock.vue';
import ItemBlockComponent from './ItemBlockComponent.vue';
import FloatActionBtnForBlock from './FloatActionBtnForBlock.vue';
import ItemBlockProvider from './ItemBlockProvider.vue';
import ImageBlock from './ImageBlock.vue';
import { useEvaluateFormStore } from 'src/stores/evaluate/form';
import { createItemBlockStore } from 'src/stores/item_block_store';
import { defaultBlocks } from 'src/data/defaultBlocks';
import draggable from 'vuedraggable';
import { AssessmentService } from 'src/services/quiz/assessmentService';

const props = defineProps<{
  blocks: ItemBlock[];
  type: 'quiz' | 'evaluate';
  assessmentId?: number | null;
}>();

// Use the new block creator store
const store = useBlockCreatorStore();
const evaluateFormStore = useEvaluateFormStore();

// Initialize assessment service based on type
const assessmentService = new AssessmentService(props.type);

// Helper function to refresh assessment data
const refreshAssessmentData = async () => {
  const assessmentId = props.assessmentId || evaluateFormStore.currentAssessment?.id;
  if (assessmentId && props.type === 'evaluate') {
    try {
      await evaluateFormStore.fetchAssessmentById(assessmentId);
    } catch (error) {
      console.error('Failed to refresh assessment data:', error);
    }
  }
};

// State management
const selectedBlock = ref<ItemBlock>();
const allowFocusFab = ref(true);
const isDragging = ref(false);

// Draggable blocks computed property
const draggableBlocks = computed({
  get: () => store.blocks,
  set: (newBlocks: ItemBlock[]) => {
    store.updateBlocksOrder(newBlocks);
  },
});

onMounted(async () => {
  // Use provided blocks or default blocks if none provided
  const blocksToUse = props.blocks && props.blocks.length > 0 ? props.blocks : defaultBlocks;
  store.initializeBlocks(blocksToUse);

  // Initialize with first block selected
  if (store.blocks.length > 0) {
    store.selectedBlockId = `block-${store.blocks[0]!.id}`;
    await nextTick();
    scrollToTarget();
  }
});

// Utility functions
let nextId =
  Math.max(
    ...(props.blocks && props.blocks.length > 0 ? props.blocks : defaultBlocks).map((b) => b.id),
    0,
  ) + 1;

// Block refs for scrolling/focus
async function setFabAndScroll(id: number) {
  allowFocusFab.value = false;
  store.selectedBlockId = `block-${id}`;
  await nextTick();
  await nextTick();
  scrollToTarget();
  setTimeout(() => (allowFocusFab.value = true), 100);
}

function scrollToTarget() {
  if (!store.selectedBlockId) return;
  const id = Number(store.selectedBlockId.split('-')[1]);
  const el = blockRefs[id];
  if (el && 'scrollIntoView' in el) {
    (el as HTMLElement).scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
}

const setSelectedBlock = (item: ItemBlock) => {
  selectedBlock.value = item;
  store.selectedBlockId = `block-${item.id}`;
};

// Helper function to determine the correct section for a new block
function getCurrentSection(index: number): number {
  // Find the section of the block at the given index
  const currentBlock = store.blocks[index];
  if (currentBlock) {
    return currentBlock.section;
  }

  // If no current block, find the section of the nearest previous block
  for (let i = index - 1; i >= 0; i--) {
    const block = store.blocks[i];
    if (block) {
      return block.section;
    }
  }

  // Default to section 1 if no blocks found
  return 1;
}

// Block operations
const handleAddBlockAfter = async (index: number) => {
  try {
    // Validate assessmentId
    const assessmentId = props.assessmentId || evaluateFormStore.currentAssessment?.id;
    if (!assessmentId) {
      console.error('Assessment ID is required but not found');
      return;
    }

    const newId = nextId++;
    const globalIsRequired = evaluateFormStore.currentAssessment?.globalIsRequired ?? false;
    const currentSection = getCurrentSection(index);

    const newBlock: ItemBlock = {
      id: newId,
      assessmentId: assessmentId,
      sequence: index + 2,
      section: currentSection,
      type: 'RADIO',
      isRequired: globalIsRequired,
    };

    console.log('Adding new block with payload:', newBlock);

    // Call backend API to add the block
    const addedBlock = await assessmentService.addBlock(newBlock);

    if (addedBlock) {
      console.log('Block successfully added to backend:', addedBlock);

      // Add to local store with backend response data
      store.addBlock(addedBlock, index);

      // Refresh assessment data to ensure UI is in sync
      await refreshAssessmentData();

      // Log full assessment JSON for debugging
      console.log(
        'Current assessment after adding block:',
        JSON.stringify(evaluateFormStore.currentAssessment, null, 2),
      );
      console.log('Current blocks in store:', JSON.stringify(store.getAssessmentData(), null, 2));

      await setFabAndScroll(addedBlock.id);
    } else {
      console.error('Failed to add block - no response from backend');
    }
  } catch (error) {
    console.error('Error adding block:', error);
  }
};

const handleAddHeaderAfter = async (index: number) => {
  const newId = nextId++;
  const currentSection = getCurrentSection(index);

  const newBlock: ItemBlock = {
    id: newId,
    assessmentId: props.blocks[0]?.assessmentId || 1,
    sequence: index + 2,
    section: currentSection,
    type: 'HEADER',
    isRequired: false,
    headerBody: {
      id: newId,
      itemBlockId: newId,
      title: '',
      description: '',
    },
  };

  store.addBlock(newBlock, index);
  await setFabAndScroll(newId);
};

const handleAddSection = async () => {
  const headerId = nextId++;
  const itemId = nextId++;
  const globalIsRequired = evaluateFormStore.currentAssessment?.globalIsRequired ?? false;

  // Calculate the new section number
  const newSectionNumber = store.totalSections + 1;

  // Calculate the next sequence numbers based on the current blocks length
  const nextHeaderSequence = store.blocks.length + 1;
  const nextItemSequence = store.blocks.length + 2;

  // Create a new header block that starts the new section
  const headerBlock: ItemBlock = {
    id: headerId,
    assessmentId: props.blocks[0]?.assessmentId || 1,
    sequence: nextHeaderSequence,
    section: newSectionNumber,
    type: 'HEADER',
    isRequired: false,
    headerBody: {
      id: headerId,
      itemBlockId: headerId,
      title: '',
      description: '',
    },
  };

  // Create a new item block in the same section
  const itemBlock: ItemBlock = {
    id: itemId,
    assessmentId: props.blocks[0]?.assessmentId || 1,
    sequence: nextItemSequence,
    section: newSectionNumber,
    type: 'RADIO',
    isRequired: globalIsRequired,
    questions: [
      {
        id: store.generateQuestionId(),
        itemBlockId: itemId,
        questionText: '',
        isHeader: false,
        sequence: 1,
        score: 0,
      },
    ],
    options: [
      {
        id: store.generateOptionId(),
        itemBlockId: itemId,
        optionText: 'ตัวเลือกที่ 1',
        value: 0,
        sequence: 1,
      },
    ],
  };

  // Append the header block to the end of the blocks array
  store.appendBlock(headerBlock);
  // Append the item block to the end of the blocks array
  store.appendBlock(itemBlock);

  // Focus on the new header block
  await setFabAndScroll(headerId);
};

const handleDuplicateHeaderBlock = async (source: ItemBlock, index: number) => {
  if (source.type !== 'HEADER') return;

  // Generate a new ID for the duplicated header block
  const newId = nextId++;
  const currentSection = getCurrentSection(index);

  // Create a new header block with the same properties as the source block
  const newBlock: ItemBlock = {
    ...source,
    id: newId,
    sequence: index + 2,
    section: currentSection,
    headerBody: {
      id: newId,
      itemBlockId: newId,
      title: source.headerBody?.title || '',
      description: source.headerBody?.description || '',
    },
  };

  // Add the new block to the blocks array
  store.addBlock(newBlock, index);
  await setFabAndScroll(newId);
};

const handleDuplicateBlock = async (source: ItemBlock, index: number) => {
  if (source.type === 'HEADER' || source.type === 'IMAGE') return;

  // Generate a new ID for the duplicated block
  const newId = nextId++;
  const currentSection = getCurrentSection(index);

  // Create a new block with the same properties as the source block
  const newBlock: ItemBlock = {
    ...source,
    id: newId,
    sequence: index + 2,
    section: currentSection,
  };

  // Add the new block to the blocks array
  store.addBlock(newBlock, index);

  // Get the source block's store to access its data
  const sourceStore = createItemBlockStore(source.id, source);

  // Get the new block's store
  const newStore = createItemBlockStore(newId, newBlock);

  // Copy data from the source store to the new store based on the question type
  if (source.type === 'RADIO') {
    // Copy radio choices
    const sourceStoreInstance = sourceStore();
    const newStoreInstance = newStore();
    newStoreInstance.radioOptions = sourceStoreInstance.radioOptions.map((choice) => ({
      placeholder: choice.placeholder,
      value: choice.value,
      optionText: choice.optionText,
      score: choice.score,
      sequence: choice.sequence,
    }));
  } else if (source.type === 'CHECKBOX') {
    // Copy checkbox choices
    const sourceStoreInstance = sourceStore();
    const newStoreInstance = newStore();
    newStoreInstance.checkboxOptions = sourceStoreInstance.checkboxOptions.map((choice) => ({
      placeholder: choice.placeholder,
      value: choice.value,
      optionText: choice.optionText,
      score: choice.score,
      sequence: choice.sequence,
    }));
  } else if (source.type === 'GRID') {
    // Copy grid row questions and column choices
    const sourceStoreInstance = sourceStore();
    const newStoreInstance = newStore();
    newStoreInstance.gridRowQuestions = sourceStoreInstance.gridRowQuestions.map((question) => ({
      label: question.label,
      value: question.value,
      sequence: question.sequence,
    }));
    newStoreInstance.gridColumnOptions = sourceStoreInstance.gridColumnOptions.map((choice) => ({
      label: choice.label,
      value: choice.value,
      optionText: choice.optionText,
      score: choice.score,
      sequence: choice.sequence,
    }));
  }

  await setFabAndScroll(newId);
};

const onClickDeleteBlock = async (_item: ItemBlock, index: number) => {
  store.deleteBlock(index);
  if (store.blocks.length > 0) {
    const nearestIndex = Math.min(index, store.blocks.length - 1);
    const nearestBlock = store.blocks[nearestIndex];
    if (nearestBlock) {
      store.selectedBlockId = `block-${nearestBlock.id}`;
      await setFabAndScroll(nearestBlock.id);
    }
  } else {
    store.selectedBlockId = undefined;
  }
};

const handleAddImage = async (index: number, imageData: string) => {
  const newId = nextId++;
  const currentSection = getCurrentSection(index);

  const newBlock: ItemBlock = {
    id: newId,
    assessmentId: props.blocks[0]?.assessmentId || 1,
    sequence: index + 2,
    section: currentSection,
    type: 'IMAGE',
    isRequired: false,
    imageBody: {
      id: newId,
      itemBlockId: newId,
      imagePath: imageData,
      imageText: '',
    },
  };

  store.addBlock(newBlock, index);
  await setFabAndScroll(newId);
};

// Drag and drop event handlers
const onDragStart = () => {
  isDragging.value = true;
  allowFocusFab.value = false;
};

const onDragEnd = () => {
  isDragging.value = false;
  setTimeout(() => {
    allowFocusFab.value = true;
  }, 100);
};

const onDragChange = (event: { added?: unknown; removed?: unknown; moved?: unknown }) => {
  // Handle drag change events if needed
  console.log('Drag change:', event);
};

// Watch for selectedBlockId changes to scroll
watch(
  () => store.selectedBlockId,
  () => {
    scrollToTarget();
  },
);

// Block refs for template
const blockRefs: Record<number, Element | ComponentPublicInstance | null> = new Proxy(
  {},
  {
    get(_target, id: string) {
      return store.getBlockRef(Number(id));
    },
    set(_target, id: string, el) {
      store.setBlockRef(Number(id), el);
      return true;
    },
  },
);
</script>

<style scoped>
.fixed-fab-col {
  width: 48px;
}

.section-container {
  position: relative;
  z-index: 1;
  margin-bottom: 0;
}

.section-tab {
  background-color: #673ab7;
  color: white;
  font-weight: 500;
  padding: 6px 16px;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  font-size: 14px;
  width: fit-content;
  position: relative;
  left: 0;
}

.no-top-left-radius {
  border-top-left-radius: 0 !important;
}

/* Drag and Drop Styles */
.draggable-item {
  transition: all 0.3s ease;
  margin-bottom: 8px;
}

.draggable-item.is-dragging {
  opacity: 0.8;
}

.block-container {
  position: relative;
  width: 100%;
}

.block-content {
  flex: 1;
}

/* Drag states */
.ghost {
  opacity: 0.5;
  background: #f0f0f0;
  border: 2px dashed #ccc;
}

.chosen {
  opacity: 0.8;
}

.drag {
  opacity: 0.5;
  transform: rotate(5deg);
}

/* Flip animation for smooth transitions */
.flip-list-move,
.flip-list-enter-active,
.flip-list-leave-active {
  transition: all 0.3s ease;
}

.flip-list-enter-from,
.flip-list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.flip-list-leave-active {
  position: absolute;
}
</style>

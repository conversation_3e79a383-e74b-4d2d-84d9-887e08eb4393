<template>
  <q-card class="q-pa-lg q-ma-md item-block-container" @click="$emit('focus-fab')">
    <!-- Header with three-dot menu and save indicator -->
    <div class="item-top-bar">
      <div class="row items-center justify-between full-width">
        <!-- Auto-save indicator -->
        <div v-if="isSaving" class="row items-center q-gutter-xs text-grey-6">
          <q-spinner size="12px" color="grey-6" />
          <span class="text-caption">Saving...</span>
        </div>
        <div v-else class="invisible"><!-- Spacer --></div>

        <!-- Three-dot menu -->
        <q-btn flat round color="grey-6" size="sm" class="three-dot-menu" @click.stop="toggleMenu">
          <ThreeDots size="xs" color="grey-6" />
          <q-menu v-model="showMenu" anchor="bottom middle" self="top middle">
            <q-list style="min-width: 150px">
              <q-item clickable v-close-popup @click="onDuplicate">
                <q-item-section avatar>
                  <q-icon name="content_copy" />
                </q-item-section>
                <q-item-section>Duplicate</q-item-section>
              </q-item>
              <q-item clickable v-close-popup @click="onDelete">
                <q-item-section avatar>
                  <q-icon name="delete" />
                </q-item-section>
                <q-item-section>Delete</q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-btn>
      </div>
    </div>

    <!-- โหมดปกติ -->
    <template v-if="!showAnswerSettings">
      <div class="row">
        <div class="col">
          <EditorTool
            label="พิมพ์คำถาม..."
            v-model:content="headerText"
            @blur="saveHeaderText(headerText)"
          />
        </div>
        <div class="col-1 q-ml-sm">
          <q-btn flat round icon="image" color="grey" class="bg-transparent" padding="sm" />
        </div>
        <div class="col-auto">
          <q-select
            v-model="selectedBlockBody"
            :options="blockBodyOptions"
            filled
            dense
            style="min-width: 200px"
            color="accent"
            option-label="label"
            map-options
            @update:model-value="onBlockBodyChange"
          >
            <template v-slot:option="scope">
              <q-item v-bind="scope.itemProps">
                <q-item-section avatar>
                  <q-icon :name="scope.opt.icon" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ scope.opt.label }}</q-item-label>
                </q-item-section>
              </q-item>
            </template>
            <template v-slot:selected>
              <div class="row items-center">
                <q-icon :name="selectedBlockBody?.icon" class="q-mr-sm" />
                <div>{{ selectedBlockBody?.label }}</div>
              </div>
            </template>
          </q-select>
        </div>
      </div>

      <component :is="currentComponent" :item-block="itemBlock" class="q-mb-md q-pt-md q-ml-md" />

      <q-separator inset color="#898989" />

      <div class="row items-center justify-between q-mt-md no-wrap">
        <div class="col-auto">
          <div v-if="props.type === 'quiz'" class="row items-center q-gutter-sm">
            <q-btn
              class="text-accent"
              icon="event_available"
              label="เฉลยคำตอบ"
              @click="toggleAnswerSettings"
            />
            <div class="text-caption text-grey-7">(0 point)</div>
          </div>
        </div>
        <div class="col-auto">
          <ItemBlockFooter
            label="จำเป็น"
            @duplicate="onClickDuplicateItem"
            @delete="onClickDeleteItem"
          />
        </div>
      </div>
    </template>

    <template v-else>
      <div class="row items-center text-h6 q-mb-md text-black">
        <q-icon name="event_available" class="q-mr-sm" size="md" />
        <div>เลือกคำตอบที่ถูกต้อง</div>
      </div>

      <div class="q-mb-md q-pl-sm">
        <div class="row items-center q-gutter-sm">
          <!-- ข้อความคำถาม -->
          <div class="text-subtitle1 text-weight-medium text-black">
            {{ headerText || 'คำถาม' }}
          </div>

          <q-space />
          <q-input
            v-model.number="number"
            type="number"
            filled
            :min="0"
            :max="100"
            step="1"
            style="max-width: 100px"
            dense
          />
        </div>
      </div>

      <div class="q-gutter-sm q-ml-sm q-mb-md">
        <div
          v-for="(choice, index) in itemBlock.options"
          :key="index"
          class="row items-center q-pa-sm rounded-borders cursor-pointer"
          :class="{
            'bg-green-1 text-green-10': selectedAnswer === choice.value,
            'bg-grey-2': selectedAnswer !== choice.value,
          }"
          @click="selectedAnswer = choice.value"
        >
          <q-radio
            v-model="selectedAnswer"
            :val="choice.value"
            size="sm"
            color="green"
            class="q-mr-sm"
          />

          <div class="text-body1">{{ choice.optionText }}</div>

          <!-- ไอคอนถูก -->
          <q-space />
          <q-icon v-if="selectedAnswer === choice.value" name="check" color="green" size="sm" />
        </div>
      </div>
      <!-- ปุ่ม -->
      <div class="row items-center q-mt-md">
        <div class="col-auto">
          <q-btn
            v-if="props.type === 'quiz'"
            class="text-accent"
            icon="event_available"
            label="เพิ่มคำตอบข้อเสนอแนะ"
          />
        </div>
        <q-space />
        <div class="col-auto">
          <q-btn color="accent" flat label="เสร็จสิ้น" @click="showAnswerSettings = false" />
        </div>
      </div>
    </template>
  </q-card>
</template>

<script setup lang="ts">
import { computed, ref, provide, onUnmounted, watch } from 'vue';
import type { Component, Ref } from 'vue';
import type { DropdownItemBlockType, ItemBlock } from 'src/types/models';
import ItemBlockFooter from 'src/components/common/blocks/ItemBlockFooter.vue';
import EditorTool from 'src/components/common/EditorTool.vue';
import ThreeDots from 'src/components/common/ThreeDots.vue';
import type { BlockBodyOptionsType } from 'src/types/app';
import OptionBody from './OptionBody.vue';
import TextBody from './TextBody.vue';
import CheckBoxBody from './CheckBoxBody.vue';
import GridBody from './GridBody.vue';
import FileUploadBody from './FileUploadBody.vue';
import { blockBodyOptions } from 'src/data/blocks';
import { extractBlockBodyType } from 'src/utils/block_helper';
import { AssessmentService } from 'src/services/quiz/assessmentService';
const number = ref<number>(0);
const selectedAnswer = ref<number | null>(null);
const emit = defineEmits([
  'focus-fab',
  'duplicate',
  'delete',
  'update:question',
  'update:type',
  'update:isRequired',
]);
const showAnswerSettings = ref(false);

function toggleAnswerSettings() {
  showAnswerSettings.value = !showAnswerSettings.value;
}
const props = defineProps<{
  itemBlock: ItemBlock;
  type: 'quiz' | 'evaluate';
}>();

// Initialize assessment service for auto-save
const assessmentService = new AssessmentService(props.type);

// Make itemBlock accessible in the template
const itemBlock = props.itemBlock;

const headerText = ref<string>('');

// Menu state
const showMenu = ref(false);

// Initialize header text based on the itemBlock prop (if available)
if (props.itemBlock && props.itemBlock.headerBody) {
  headerText.value = props.itemBlock.headerBody.title || '';
}

const saveHeaderText = (text: string) => {
  // * save header text to server
  console.log('Header text saved:', text);
};

const selectedBlockBody = ref<BlockBodyOptionsType>(
  extractBlockBodyType(props.itemBlock) || blockBodyOptions[0]!,
);

const onBlockBodyChange = (value: BlockBodyOptionsType) => {
  selectedBlockBody.value = value;
  // Emit the change to parent component
  emit('update:type', value);

  // Trigger auto-save for block type change
  debouncedAutoSaveBlock('type', value.value);
};

// Auto-save functionality
const DEBOUNCE_DELAY = 500; // 500ms debounce delay as specified
const blockDebounceTimeout = ref<number | null>(null);
const questionDebounceTimeouts = ref<Map<number, number>>(new Map());
const isSaving = ref(false);

// Track last saved values to detect changes
const lastSavedBlockData = ref<Partial<ItemBlock>>({
  type: props.itemBlock.type,
  sequence: props.itemBlock.sequence,
  isRequired: props.itemBlock.isRequired,
});

// Clear debounce timeout helper
function clearDebounceTimeout(timeoutRef: Ref<number | null>) {
  if (timeoutRef.value) {
    clearTimeout(timeoutRef.value);
    timeoutRef.value = null;
  }
}

// Clear question-specific timeout
function clearQuestionTimeout(questionId: number) {
  const timeout = questionDebounceTimeouts.value.get(questionId);
  if (timeout) {
    clearTimeout(timeout);
    questionDebounceTimeouts.value.delete(questionId);
  }
}

// Debounced auto-save for ItemBlock
function debouncedAutoSaveBlock(field: string, value: string | number | boolean) {
  // Clear existing timeout
  clearDebounceTimeout(blockDebounceTimeout);

  // Set new timeout for auto-save
  blockDebounceTimeout.value = window.setTimeout(() => {
    // Only save if value has changed
    const currentValue = lastSavedBlockData.value[field as keyof ItemBlock];
    if (currentValue !== value) {
      performBlockAutoSave(field, value).catch((error) => {
        console.error(`Auto-save error for block ${field}:`, error);
      });
    }
  }, DEBOUNCE_DELAY);
}

// Debounced auto-save for Questions
function debouncedAutoSaveQuestion(
  questionId: number,
  field: string,
  value: string | number | boolean,
) {
  // Clear existing timeout for this question
  clearQuestionTimeout(questionId);

  // Set new timeout for auto-save
  const timeout = window.setTimeout(() => {
    performQuestionAutoSave(questionId, field, value).catch((error) => {
      console.error(`Auto-save error for question ${questionId} ${field}:`, error);
    });
  }, DEBOUNCE_DELAY);

  questionDebounceTimeouts.value.set(questionId, timeout);
}

// Perform ItemBlock auto-save
async function performBlockAutoSave(field: string, value: string | number | boolean) {
  try {
    // Validate ItemBlock ID
    if (!props.itemBlock.id) {
      console.warn('Cannot auto-save block: Missing ItemBlock ID');
      return;
    }

    isSaving.value = true;

    console.log(`Auto-saving ItemBlock ${field}:`, {
      itemBlockId: props.itemBlock.id,
      field,
      value,
    });

    // Prepare update payload
    const updatePayload: Partial<ItemBlock> = {
      id: props.itemBlock.id,
      type: props.itemBlock.type,
      sequence: props.itemBlock.sequence,
      [field]: value,
    };

    // Call updateBlock API
    const updatedBlock = await assessmentService.updateBlock(updatePayload as ItemBlock);

    if (updatedBlock) {
      // Update last saved values (using type assertion to handle the complex typing)
      (lastSavedBlockData.value as Record<string, unknown>)[field] = value;

      console.log(`ItemBlock ${field} auto-saved successfully:`, updatedBlock);
    }
  } catch (error) {
    console.error(`Failed to auto-save ItemBlock ${field}:`, error);
  } finally {
    isSaving.value = false;
  }
}

// Perform Question auto-save
async function performQuestionAutoSave(
  questionId: number,
  field: string,
  value: string | number | boolean,
) {
  try {
    // Find the question in the ItemBlock
    const question = props.itemBlock.questions?.find((q) => q.id === questionId);
    if (!question) {
      console.warn(`Cannot auto-save question: Question ${questionId} not found`);
      return;
    }

    isSaving.value = true;

    console.log(`Auto-saving Question ${field}:`, {
      questionId,
      field,
      value,
    });

    // Prepare update payload
    const updatePayload = {
      ...question,
      [field]: value,
    };

    // Call updateQuestion API
    const updatedQuestion = await assessmentService.updateQuestion(questionId, updatePayload);

    if (updatedQuestion) {
      console.log(`Question ${field} auto-saved successfully:`, updatedQuestion);
    }
  } catch (error) {
    console.error(`Failed to auto-save Question ${field}:`, error);
  } finally {
    isSaving.value = false;
  }
}

// Cleanup function
onUnmounted(() => {
  // Clear all timeouts when component is unmounted
  clearDebounceTimeout(blockDebounceTimeout);

  // Clear all question timeouts
  questionDebounceTimeouts.value.forEach((timeout) => {
    clearTimeout(timeout);
  });
  questionDebounceTimeouts.value.clear();
});

// Provide auto-save functions to child components
provide('autoSave', {
  debouncedAutoSaveQuestion,
  debouncedAutoSaveBlock,
});

// Watch for prop changes to update last saved values
watch(
  () => props.itemBlock.type,
  (newType) => {
    if (newType !== undefined) {
      lastSavedBlockData.value.type = newType;
    }
  },
);

watch(
  () => props.itemBlock.sequence,
  (newSequence) => {
    if (newSequence !== undefined) {
      lastSavedBlockData.value.sequence = newSequence;
    }
  },
);

watch(
  () => props.itemBlock.isRequired,
  (newIsRequired) => {
    if (newIsRequired !== undefined) {
      lastSavedBlockData.value.isRequired = newIsRequired;
    }
  },
);

// Initialize component when mounted
// onMounted(() => {
//   // If it's a grid type, set up the header question
//   if (props.selectedType === 'grid') {
//     // Set up grid questions
//     setupGridQuestions();

//     // Ensure all column choices have score set to 0
//     store?.gridColumnOptions.forEach((choice) => {
//       if (choice) {
//         choice.score = 0;
//       }
//     });

//     // If there are no row questions yet, add at least one
//     if (store?.gridRowQuestions.length === 0) {
//       store?.addRowQuestion();
//       // Set up grid questions again
//       setupGridQuestions();
//     }

//     // Make sure we have a main question text for the header
//     if (!questionTextModel.value || questionTextModel.value.trim() === '') {
//       // Set a default main question if none exists
//       emit('update:question', 'คำถามหลัก');
//       console.log('Set default grid header question on mount');
//     }
//   }
// });

const onClickDuplicateItem = () => {
  // * implement duplication logic here
};

const onClickDeleteItem = () => {
  // * implement deletion logic here
  emit('delete');
};

// Menu handler functions
function toggleMenu() {
  showMenu.value = !showMenu.value;
}

function onDuplicate() {
  emit('duplicate');
}

function onDelete() {
  emit('delete');
}

const currentComponent = computed<Component>(() => {
  if (!selectedBlockBody.value) {
    return OptionBody; // Default component if none selected
  }
  type ComponentType = Record<DropdownItemBlockType, Component>;

  const components: ComponentType = {
    RADIO: OptionBody,
    TEXTFIELD: TextBody,
    CHECKBOX: CheckBoxBody,
    GRID: GridBody,
    FILE: FileUploadBody,
  };

  return components[selectedBlockBody.value.value];
});
</script>

<style scoped>
.q-select {
  box-sizing: border-box;
  width: 255px;
  background: #fffdfd;
  border: 1px solid #b1b1b1;
  border-radius: 10px;
}

.main-question-input {
  transition: all 0.3s ease;
}

.item-block-container {
  position: relative;
}

.item-top-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: -16px;
  margin-left: -32px;
  margin-right: -32px;
  margin-bottom: 8px;
}

.three-dot-menu {
  background-color: transparent;
  border: none;
  border-radius: 0;
  padding: 4px;
  cursor: grab;
  outline: none;
}

.three-dot-menu:hover {
  background-color: transparent;
}

.three-dot-menu:active {
  cursor: grabbing;
}

.three-dot-menu:focus {
  outline: none;
  box-shadow: none;
}
</style>

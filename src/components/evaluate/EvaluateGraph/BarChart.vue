<template>
  <canvas ref="canvas"></canvas>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, defineExpose } from 'vue';
import {
  Chart,
  BarController,
  BarElement,
  CategoryScale,
  LinearScale,
  Tooltip,
  Legend,
} from 'chart.js';

Chart.register(BarController, BarElement, CategoryScale, LinearScale, Tooltip, Legend);

interface Dataset {
  label: string;
  values: number[];
}

const props = defineProps<{
  labels: string[];
  data: Dataset[];
}>();

const canvas = ref<HTMLCanvasElement | null>(null);
let chartInstance: Chart | null = null;

function generateColors(count: number): string[] {
  const baseColors = ['#3d3c91', '#6a4cb3', '#a65fd3', '#d173e7', '#ff87ff'];
  return Array.from({ length: count }, (_, i) => baseColors[i % baseColors.length]!);
}

function transformChartData(data: { labels: string[]; datasets: Dataset[] }) {
  // กรณี labels มีแค่ 1 ตัวเท่านั้น จะทำการ transpose data
  if (!data.labels || data.labels.length !== 1) return data;

  if (!data.datasets || data.datasets.length === 0) return { labels: [], datasets: [] };

  const valuesMatrix = data.datasets.map((d) => d.values ?? []);
  if (valuesMatrix.length === 0 || valuesMatrix[0]!.length === 0)
    return { labels: [], datasets: [] };

  // Transpose matrix
  const transposed = valuesMatrix[0]!.map((_, colIndex) =>
    valuesMatrix.map((row) => row[colIndex] ?? 0),
  );

  // สร้าง dataset ใหม่หลัง transpose
  const newDatasets: Dataset[] = transposed.map((vals, idx) => ({
    label: data.labels[idx] ?? '',
    values: vals,
  }));

  return {
    labels: data.datasets.map((d) => d.label ?? ''),
    datasets: newDatasets,
  };
}

function renderChart() {
  if (!canvas.value) return;
  if (chartInstance) chartInstance.destroy();

  const colors = generateColors(props.data.length);
  const isSingleLabel = props.labels.length === 1;

  const chartData = isSingleLabel
    ? transformChartData({ labels: props.labels, datasets: props.data })
    : { labels: props.labels, datasets: props.data };

  const datasets = chartData.datasets.map((ds, idx) => ({
    label: ds.label,
    data: ds.values,
    backgroundColor: colors[idx],
  }));

  chartInstance = new Chart(canvas.value, {
    type: 'bar',
    data: {
      labels: chartData.labels,
      datasets,
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        x: {
          display: true,
        },
        y: {
          beginAtZero: true,
        },
      },
      plugins: {
        legend: {
          display: props.labels.length > 1, // แสดง legend ก็ต่อเมื่อ labels มากกว่า 1
        },
      },
    },
  });
}

onMounted(renderChart);

watch(
  () => [props.labels, props.data],
  () => {
    renderChart();
  },
  { deep: true },
);

onBeforeUnmount(() => {
  if (chartInstance) chartInstance.destroy();
});

defineExpose({ canvas });
</script>

<style scoped>
canvas {
  width: 100% !important;
  height: 300px !important;
}
</style>

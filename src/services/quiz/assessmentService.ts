import { api as axios } from 'src/boot/axios';
import type { QTableProps } from 'quasar';
import type { DataResponse } from 'src/types/data';
import type { Assessment, ItemBlock, HeaderBody } from 'src/types/models';
import { formatParams } from 'src/utils/utils';
import { Notify } from 'quasar';
import { useGlobalStore } from 'src/stores/global';
const globalStore = useGlobalStore();
type PathName = 'quiz' | 'evaluate';

const showError = (message: string) => {
  Notify.create({
    message,
    type: 'negative',
    position: 'bottom',
    timeout: 3000,
  });
};
export class AssessmentService {
  path = '';
  headerBodyPath = '';
  questionsPath = '';

  constructor(path: PathName) {
    this.path = path + '/assessments';
    this.headerBodyPath = path + '/header-bodies';
    this.questionsPath = path + '/questions';
  }

  async fetchAll(pag: QTableProps['pagination'], search?: string) {
    try {
      const params = formatParams(pag, search);
      const res = await axios.get<DataResponse<Assessment>>(this.path, { params });
      return res.data;
    } catch {
      showError('ไม่สามารถโหลดรายการแบบประเมินได้');
      throw new Error('Fetch assessments failed');
    }
  }

  async fetchOne(id: number) {
    try {
      const res = await axios.get<Assessment>(`${this.path}/${id}`);
      return res.data;
    } catch {
      showError('ไม่สามารถโหลดข้อมูลแบบประเมินได้');
      throw new Error('Fetch single assessment failed');
    }
  }

  async createOne(assessment: Partial<Assessment>) {
    try {
      const res = await axios.post<Assessment>(this.path, assessment);
      globalStore.Loading();
      return res.data;
    } catch {
      showError('ไม่สามารถสร้างแบบประเมินได้');
      throw new Error('Create assessment failed');
    }
  }

  async updateOne(id: number) {
    try {
      const res = await axios.patch<Assessment>(`${this.path}/${id}`);
      globalStore.Loading();
      return res.data;
    } catch {
      showError('ไม่สามารถอัปเดตแบบประเมินได้');
      throw new Error('Update assessment failed');
    }
  }

  async deleteOne(id: number) {
    try {
      const res = await axios.delete<Assessment>(`${this.path}/${id}`);
      Notify.create({ message: 'ลบเรียบร้อยแล้ว', type: 'positive' });
      return res.data;
    } catch {
      showError('ไม่สามารถลบแบบประเมินได้');
      throw new Error('Delete assessment failed');
    }
  }

  async addBlock(block: ItemBlock): Promise<ItemBlock | undefined> {
    try {
      const res = await axios.put<ItemBlock>(`${this.path}/item-blocks/${block.id}`, block);
      globalStore.Loading();
      return res.data;
    } catch {
      showError('ไม่สามารถเพิ่ม Block ได้');
      return;
    }
  }

  async updateBlock(block: ItemBlock): Promise<ItemBlock | undefined> {
    try {
      const res = await axios.patch<ItemBlock>(`${this.path}/item-blocks/${block.id}`, block);
      globalStore.Loading();
      return res.data;
    } catch {
      showError('ไม่สามารถอัปเดต Block ได้');
      return;
    }
  }

  async deleteBlock(block: ItemBlock): Promise<ItemBlock | undefined> {
    try {
      const res = await axios.delete<ItemBlock>(`${this.path}/item-blocks/${block.id}`);
      Notify.create({ message: 'ลบเรียบร้อยแล้ว', type: 'positive' });
      return res.data;
    } catch {
      showError('ไม่สามารถลบ Block ได้');
      return;
    }
  }

  // HeaderBody methods moved from HeaderBodyService
  async fetchHeaderBody(id: number): Promise<HeaderBody | undefined> {
    try {
      const res = await axios.get<HeaderBody>(`${this.headerBodyPath}/${id}`);
      return res.data;
    } catch {
      showError('ไม่สามารถโหลดข้อมูล Header Body ได้');
      return;
    }
  }

  async createHeaderBody(headerBody: Partial<HeaderBody>): Promise<HeaderBody | undefined> {
    try {
      const res = await axios.post<HeaderBody>(this.headerBodyPath, headerBody);
      globalStore.Loading();
      console.log('HeaderBody created:', res.data);
      return res.data;
    } catch {
      showError('ไม่สามารถสร้าง Header Body ได้');
      return;
    }
  }

  async updateHeaderBody(id: number, headerBody: Partial<HeaderBody>): Promise<HeaderBody | undefined> {
    try {
      const res = await axios.patch<HeaderBody>(`${this.headerBodyPath}/${id}`, headerBody);
      globalStore.Loading();
      console.log('HeaderBody updated:', res.data);
      return res.data;
    } catch {
      showError('ไม่สามารถอัปเดต Header Body ได้');
      return;
    }
  }

  async deleteHeaderBody(id: number): Promise<HeaderBody | undefined> {
    try {
      const res = await axios.delete<HeaderBody>(`${this.headerBodyPath}/${id}`);
      Notify.create({ message: 'ลบ Header Body เรียบร้อยแล้ว', type: 'positive' });
      return res.data;
    } catch {
      showError('ไม่สามารถลบ Header Body ได้');
      return;
    }
  }

  // Questions methods
  async fetchQuestions(assessmentId: number) {
    try {
      const res = await axios.get<ItemBlock[]>(`${this.questionsPath}`, { 
        params: { assessmentId } 
      });
      return res.data;
    } catch {
      showError('ไม่สามารถโหลดคำถามได้');
      throw new Error('Fetch questions failed');
    }
  }

  async createQuestion(question: Partial<ItemBlock>) {
    try {
      const res = await axios.post<ItemBlock>(this.questionsPath, question);
      globalStore.Loading();
      return res.data;
    } catch {
      showError('ไม่สามารถสร้างคำถามได้');
      throw new Error('Create question failed');
    }
  }

  async updateQuestion(id: number, question: Partial<ItemBlock>) {
    try {
      const res = await axios.patch<ItemBlock>(`${this.questionsPath}/${id}`, question);
      globalStore.Loading();
      return res.data;
    } catch {
      showError('ไม่สามารถอัปเดตคำถามได้');
      throw new Error('Update question failed');
    }
  }

  async deleteQuestion(id: number) {
    try {
      const res = await axios.delete<ItemBlock>(`${this.questionsPath}/${id}`);
      Notify.create({ message: 'ลบคำถามแล้ว', type: 'positive' });
      return res.data;
    } catch {
      showError('ไม่สามารถลบคำถามได้');
      throw new Error('Delete question failed');
    }
  } 
}
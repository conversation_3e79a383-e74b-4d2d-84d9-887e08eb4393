/* eslint-disable @typescript-eslint/no-explicit-any */
import { defineStore } from 'pinia';
import { ref } from 'vue';
import form from 'src/services/evaluate/form';
import type { Assessment } from 'src/types/models';
import type { DataResponse } from 'src/types/data';

export const useEvaluateFormStore = defineStore('evaluateForm', () => {
  // 🔹 State
  const meta = ref<DataResponse<Assessment> | null>(null);
  const assessments = ref<Assessment[]>([]);
  const currentAssessment = ref<Assessment | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);

  const page = ref(1);
  const limit = ref(5);
  const search = ref('');

  // 🔹 Actions
  const fetchAssessmentById = async (id: number) => {
    loading.value = true;
    error.value = null;
    try {
      const { data } = await form.getFormById(id);
      if (data) {
        currentAssessment.value = data;
      }
    } catch (err: any) {
      error.value = err?.message ?? 'ไม่สามารถโหลดแบบทดสอบได้';
    } finally {
      loading.value = false;
    }
  };

  const addAssessment = async (assessmentData: Partial<Assessment>): Promise<Assessment> => {
    const { data } = await form.createForm(assessmentData);
    assessments.value.push(data);
    currentAssessment.value = data;
    return data;
  };

  const updateAssessment = async (id: number, assessmentData: Assessment): Promise<Assessment> => {
    const { data } = await form.updateForm(id, assessmentData);
    const index = assessments.value.findIndex((q) => q.id === id);
    if (index !== -1) assessments.value[index] = data;
    if (currentAssessment.value?.id === id) currentAssessment.value = data;
    return data;
  };

  const removeAssessment = async (id: number): Promise<void> => {
    await form.deleteForm(id);
    assessments.value = assessments.value.filter((q) => q.id !== id);
    if (currentAssessment.value?.id === id) {
      currentAssessment.value = null;
    }
  };

  return {
    // state
    assessments,
    currentAssessment,
    loading,
    error,
    meta,
    page,
    limit,
    search,
    // actions
    fetchAssessmentById,
    addAssessment,
    updateAssessment,
    removeAssessment,
  };
});

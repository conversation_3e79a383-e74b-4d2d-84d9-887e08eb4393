<template>
  <q-page padding>
    <div class="row items-center justify-between q-mb-md">
      <div class="body">การจัดการแบบสอบถาม</div>
      <div class="row items-center q-gutter-sm">
        <SearchBar />
        <q-btn
          label="สร้าง"
          icon="add"
          class="text-white"
          color="accent"
          s
          @click="onClickCreate"
        />
      </div>
    </div>
    <EvaluateTable />
  </q-page>
</template>

<script setup lang="ts">
import EvaluateTable from 'src/components/evaluate/EvaluateTable.vue';
import SearchBar from 'src/components/SearchBar.vue';
import router from 'src/router';
import { useAuthStore } from 'src/stores/auth';
import { useEvaluateFormStore } from 'src/stores/evaluate/form';

async function onClickCreate() {
  try {
    const user = useAuthStore().getCurrentUser();
    const evaluateFormStore = useEvaluateFormStore();

    console.log('Creating new assessment using evaluate form service...');

    // Use the evaluate form store's addAssessment method which uses the correct /evaluate-form endpoint
    const res = await evaluateFormStore.addAssessment({
      creatorUserId: user?.id || 1,
      programId: 1,
      type: 'FORM',
      name: 'New Assessment',
      responseEdit: false,
      status: true,
      totalScore: 0,
      timeout: 0,
      passRatio: 0,
    });

    console.log('Assessment created successfully:', {
      id: res.id,
      itemBlocks: res.itemBlocks?.map((block) => ({
        id: block.id,
        type: block.type,
        assessmentId: block.assessmentId,
      })),
    });

    await router.push({
      name: 'evaluate-id',
      query: { mode: 'edit' },
      params: { id: res.id },
      hash: '#questions',
    });
  } catch (error) {
    console.error('Failed to create assessment:', error);
  }
}
</script>

<style scoped></style>

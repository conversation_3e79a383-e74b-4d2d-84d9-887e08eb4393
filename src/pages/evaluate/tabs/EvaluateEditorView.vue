<template>
  <q-page class="q-pa-md">
    <BlockCreator :blocks="blocks" type="evaluate" />
  </q-page>
</template>

<script setup lang="ts">
import BlockCreator from 'src/components/common/blocks/BlockCreator.vue';
import type { ItemBlock } from 'src/types/models';
import { ref } from 'vue';
import { defaultBlocks } from 'src/data/defaultBlocks';

const blocks = ref<ItemBlock[]>([...defaultBlocks]);
</script>
<style scoped></style>

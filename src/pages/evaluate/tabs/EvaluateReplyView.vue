<template>
  <q-page class="q-pa-md">
    <!-- Header -->
    <div class="relative-wrapper center-content row gap-4" ref="headerRef">
      <EvaluateHeaderResponses class="col-12 center-content mb-4"></EvaluateHeaderResponses>

      <!-- Loop through chartDatas and display each chart -->
      <div v-for="(chartData, index) in chartDatas" :key="index" class="col-12 center-content">
        <EvaluateGraph :chartData="chartData" />
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import EvaluateHeaderResponses from 'src/components/evaluate/EvaluateHeaderResponses.vue';
import EvaluateGraph from 'src/components/evaluate/EvaluateGraph/EvaluateGraph.vue';
import type { ChartData } from 'src/types/chart';
import evaluateService from 'src/services/evaluate/form';
import { onMounted, ref } from 'vue';

// const props = defineProps<{
//   id?: string;
// }>();

// const route = useRoute();
// const evaluateFormStore = useEvaluateFormStore();
// const assessmentId = ref<number | null>(null);

onMounted(async () => {
  await getResponse();
});
const chartDatas = ref<ChartData[]>([]);
const getResponse = async () => {
  try {
    const res = await evaluateService.getResponseById(4);
    // const res = await evaluateService.getResponseById(parseInt(props.id!));
    chartDatas.value = res.data || [];
  } catch (error) {
    console.error('Error fetching responses:', error);
  }
};
</script>

<style scoped>
.relative-wrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.mb-4 {
  margin-bottom: 16px;
}

.center-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
</style>

<template>
  <q-page class="bg-page-primary">
    <AsmMenuTab :menu="defaultAsmTabsMenu" v-model="selectedTab" />
    <q-tab-panels v-model="selectedTab" animated>
      <q-tab-panel
        v-for="tab in defaultAsmTabsMenu"
        :name="tab.name ?? ''"
        :key="tab.name ?? ''"
        class="bg-page-primary"
      >
        <component :is="componentMap[tab.name as string]" v-if="selectedTab === tab.name" />
      </q-tab-panel>
    </q-tab-panels>
  </q-page>
</template>

<script setup lang="ts">
import AsmMenuTab from 'src/components/common/AsmMenuTab.vue';
import { defaultAsmTabsMenu } from 'src/data/menu';
import { AssessmentService } from 'src/services/quiz/assessmentService';
import { useEvaluateFormStore } from 'src/stores/evaluate/form';
import { onMounted, ref, defineAsyncComponent } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const id = route.params.id;
const hash = route.hash;
const evaluateFormStore = useEvaluateFormStore();
const selectedTab = ref('questions');

onMounted(() => {
  if (hash) {
    selectedTab.value = hash.replace('#', '');
  } else {
    selectedTab.value = 'questions';
  }
});

const componentMap: Record<string, ReturnType<typeof defineAsyncComponent>> = {
  questions: defineAsyncComponent(() => import('./tabs/EvaluateEditorView.vue')),
  replies: defineAsyncComponent(() => import('./tabs/EvaluateReplyView.vue')),
  settings: defineAsyncComponent(() => import('./tabs/EvaluateSettingView.vue')),
};

onMounted(async () => {
  try {
    // Use the correct service for evaluate assessments and store in evaluate form store
    const data = await new AssessmentService('evaluate').fetchOne(Number(id));
    if (data) {
      evaluateFormStore.currentAssessment = data;
      console.log('Assessment loaded with ID tracking:', {
        assessmentId: data.id,
        itemBlocks: data.itemBlocks?.map((block) => ({
          id: block.id,
          type: block.type,
          assessmentId: block.assessmentId,
        })),
      });
    }
  } catch (error) {
    console.error('Failed to load assessment:', error);
  }
});
</script>

<style scoped></style>
